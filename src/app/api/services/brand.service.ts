/**
 * Brand Service
 * Business logic cho Brand management
 */

import { BaseService } from './base.service';
import { Injectable } from '../di-container';
import { 
  BrandRepository,
  ProductRepository 
} from '../repositories';
import {
  BrandEntity,
  CreateBrandData,
  UpdateBrandData,
  BrandBusinessRules
} from '../../models/brand.model';
import { 
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ConflictError,
  ValidationError
} from '../../models/common.model';
import { UserEntity } from '../../models/user.model';

// Service identifier
export const BRAND_SERVICE = Symbol('BrandService');

@Injectable
export class BrandService extends BaseService {
  private brandRepository: BrandRepository;
  private productRepository: ProductRepository;

  constructor(
    brandRepository: BrandRepository,
    productRepository: ProductRepository
  ) {
    super();
    this.brandRepository = brandRepository;
    this.productRepository = productRepository;
  }

  /**
   * Tạo brand mới
   */
  async createBrand(
    data: CreateBrandData,
    createdBy: UserEntity
  ): Promise<BrandEntity> {
    return this.executeWithErrorHandling(async () => {
      // Validate input
      this.validateRequired(data, ['name']);

      // Generate slug if not provided
      const slug = data.slug || BrandBusinessRules.generateSlug(data.name);

      // Validate slug
      if (!BrandBusinessRules.validateSlug(slug)) {
        throw new ValidationError('Invalid slug format');
      }

      // Check slug uniqueness
      const existingBrand = await this.brandRepository.findBySlug(slug);
      if (existingBrand) {
        throw new ConflictError('Brand slug already exists');
      }

      // Create brand
      const brandData = {
        ...data,
        slug,
        status: data.status || 'ACTIVE'
      };

      const brand = await this.brandRepository.create(brandData) as unknown as BrandEntity;

      // Log activity
      await this.logActivity('BRAND_CREATED', createdBy.id, {
        brandId: brand.id,
        brandName: brand.name
      });

      return brand;
    }, 'createBrand');
  }

  /**
   * Lấy brand theo ID
   */
  async getBrandById(id: string): Promise<BrandEntity> {
    return this.executeWithErrorHandling(async () => {
      const brand = await this.brandRepository.findById(id);
      if (!brand) {
        throw new NotFoundError('Brand', id);
      }
      return brand as unknown as BrandEntity;
    }, 'getBrandById');
  }

  /**
   * Lấy brand theo slug
   */
  async getBrandBySlug(slug: string): Promise<BrandEntity> {
    return this.executeWithErrorHandling(async () => {
      const brand = await this.brandRepository.findBySlug(slug);
      if (!brand) {
        throw new NotFoundError('Brand', slug);
      }
      return brand as unknown as BrandEntity;
    }, 'getBrandBySlug');
  }

  /**
   * Cập nhật brand
   */
  async updateBrand(
    id: string,
    data: UpdateBrandData,
    updatedBy: UserEntity
  ): Promise<BrandEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check if brand exists
      const existingBrand = await this.brandRepository.findById(id);
      if (!existingBrand) {
        throw new NotFoundError('Brand', id);
      }

      // Validate slug if provided
      if (data.slug) {
        if (!BrandBusinessRules.validateSlug(data.slug)) {
          throw new ValidationError('Invalid slug format');
        }

        // Check slug uniqueness (exclude current brand)
        const brandWithSlug = await this.brandRepository.findBySlug(data.slug);
        if (brandWithSlug && brandWithSlug.id !== id) {
          throw new ConflictError('Brand slug already exists');
        }
      }

      // Update brand
      const updatedBrand = await this.brandRepository.update(id, data) as unknown as BrandEntity;

      // Log activity
      await this.logActivity('BRAND_UPDATED', updatedBy.id, {
        brandId: id,
        changes: data
      });

      return updatedBrand;
    }, 'updateBrand');
  }

  /**
   * Xóa brand
   */
  async deleteBrand(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check if brand exists
      const brand = await this.brandRepository.findById(id);
      if (!brand) {
        throw new NotFoundError('Brand', id);
      }

      // Check if brand can be deleted
      if (!BrandBusinessRules.canDelete(brand as unknown as BrandEntity)) {
        throw new ValidationError('Cannot delete brand with associated products');
      }

      // Check for associated products
      const productCount = await this.productRepository.countByBrand(id);
      if (productCount > 0) {
        throw new ValidationError(`Cannot delete brand. ${productCount} products are associated with this brand`);
      }

      // Delete brand
      await this.brandRepository.delete(id);

      // Log activity
      await this.logActivity('BRAND_DELETED', deletedBy.id, {
        brandId: id,
        brandName: brand.name
      });
    }, 'deleteBrand');
  }

  /**
   * Lấy danh sách brands với phân trang
   */
  async getBrands(filters: SearchFilters = {}): Promise<PaginatedResult<BrandEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { name: { contains: searchQuery, mode: 'insensitive' } },
          { description: { contains: searchQuery, mode: 'insensitive' } }
        ];
      }

      if (filters.status) {
        searchConditions.status = filters.status;
      }

      // Get brands with pagination
      const result = await this.brandRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || 'name']: filters.sortOrder || 'asc'
        }
      });

      return {
        data: result.data.map(brand => brand as unknown as BrandEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages
      };
    }, 'getBrands');
  }

  /**
   * Lấy brands phổ biến
   */
  async getPopularBrands(limit: number = 10): Promise<BrandEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const brands = await this.brandRepository.getPopularBrands(limit);
      return brands.map(brand => brand as unknown as BrandEntity);
    }, 'getPopularBrands');
  }

  /**
   * Lấy brands featured
   */
  async getFeaturedBrands(limit: number = 6): Promise<BrandEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const brands = await this.brandRepository.getFeaturedBrands(limit);
      return brands.map(brand => brand as unknown as BrandEntity);
    }, 'getFeaturedBrands');
  }

  /**
   * Tìm kiếm brands
   */
  async searchBrands(
    query: string,
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<BrandEntity>> {
    return this.executeWithErrorHandling(async () => {
      const searchFilters = {
        ...filters,
        search: query
      };
      return await this.getBrands(searchFilters);
    }, 'searchBrands');
  }

  /**
   * Cập nhật trạng thái brand
   */
  async updateBrandStatus(
    id: string,
    status: 'ACTIVE' | 'INACTIVE',
    updatedBy: UserEntity
  ): Promise<BrandEntity> {
    return this.executeWithErrorHandling(async () => {
      const brand = await this.updateBrand(id, { status }, updatedBy);

      // Log activity
      await this.logActivity('BRAND_STATUS_UPDATED', updatedBy.id, {
        brandId: id,
        newStatus: status
      });

      return brand;
    }, 'updateBrandStatus');
  }

  /**
   * Lấy thống kê brand
   */
  async getBrandStats(id: string): Promise<{
    productCount: number;
    activeProductCount: number;
    totalRevenue: number;
    averageRating: number;
  }> {
    return this.executeWithErrorHandling(async () => {
      const brand = await this.brandRepository.findById(id);
      if (!brand) {
        throw new NotFoundError('Brand', id);
      }

      const [
        productCount,
        activeProductCount,
        // TODO: Implement revenue and rating calculations
      ] = await Promise.all([
        this.productRepository.countByBrand(id),
        this.productRepository.countByBrand(id, { status: 'ACTIVE' }),
      ]);

      return {
        productCount,
        activeProductCount,
        totalRevenue: 0, // TODO: Calculate from orders
        averageRating: 0, // TODO: Calculate from reviews
      };
    }, 'getBrandStats');
  }
}
